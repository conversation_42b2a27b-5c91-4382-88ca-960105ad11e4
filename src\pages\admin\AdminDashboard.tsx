import React from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardContent } from '../../components/ui/Card';
import { 
  UsersIcon, 
  BuildingIcon, 
  GiftIcon, 
  TrendingUpIcon,
  BarChart3Icon,
  MapPinIcon
} from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string;
  change: string;
  icon: React.ReactNode;
}

function StatCard({ title, value, change, icon }: StatCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-500">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            <p className="text-xs text-green-600">{change}</p>
          </div>
          <div className="h-8 w-8 text-gray-400">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function AdminDashboard() {
  const stats = [
    {
      title: 'Total Users',
      value: '2,847',
      change: '+12% from last month',
      icon: <UsersIcon className="h-8 w-8" />,
    },
    {
      title: 'Active Companies',
      value: '156',
      change: '+8% from last month',
      icon: <BuildingIcon className="h-8 w-8" />,
    },
    {
      title: 'Flawagrams Sent',
      value: '12,459',
      change: '+23% from last month',
      icon: <GiftIcon className="h-8 w-8" />,
    },
    {
      title: 'Monthly Revenue',
      value: '$45,230',
      change: '+15% from last month',
      icon: <TrendingUpIcon className="h-8 w-8" />,
    },
  ];

  const topCategories = [
    { category: 'Digital', count: 4250, percentage: 34 },
    { category: 'Edibles', count: 3100, percentage: 25 },
    { category: 'Wellness', count: 2480, percentage: 20 },
    { category: 'Impact Gifts', count: 1860, percentage: 15 },
    { category: 'Experiences', count: 769, percentage: 6 },
  ];

  const topRegions = [
    { region: 'North America', count: 8500, percentage: 68 },
    { region: 'Europe', count: 2400, percentage: 19 },
    { region: 'Asia Pacific', count: 1200, percentage: 10 },
    { region: 'Other', count: 359, percentage: 3 },
  ];

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Platform Dashboard</h1>
        <p className="text-gray-500">Overview of platform metrics and activity</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Gift Categories */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-base font-medium">Top Gift Categories</CardTitle>
            <BarChart3Icon className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topCategories.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-black rounded-full"></div>
                    <span className="text-sm font-medium">{item.category}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{item.count}</span>
                    <span className="text-xs text-gray-400">({item.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Regions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-base font-medium">Activity by Region</CardTitle>
            <MapPinIcon className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topRegions.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
                    <span className="text-sm font-medium">{item.region}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{item.count}</span>
                    <span className="text-xs text-gray-400">({item.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">Recent Platform Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium">New company registration</p>
                <p className="text-xs text-gray-500">GreenTech Solutions joined the platform</p>
              </div>
              <span className="text-xs text-gray-400">2 hours ago</span>
            </div>
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium">High-volume sender</p>
                <p className="text-xs text-gray-500">TechCorp sent 50+ Flawagrams this week</p>
              </div>
              <span className="text-xs text-gray-400">5 hours ago</span>
            </div>
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium">Gift inventory alert</p>
                <p className="text-xs text-gray-500">Spa Gift Set running low (5 remaining)</p>
              </div>
              <span className="text-xs text-gray-400">1 day ago</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
