import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FlawagramForm } from '../../components/dashboard/FlawagramForm';
import { FlawagramFormData } from '../../types';
import { useData } from '../../contexts/DataContext';

export function SendFlawagram() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');
  const { sendFlawagram } = useData();
  const navigate = useNavigate();

  const handleSubmit = async (data: FlawagramFormData) => {
    try {
      setError('');
      setIsSubmitting(true);
      await sendFlawagram(data);
      navigate('/dashboard/outbox');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send Flawagram');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Send a Flawagram</h1>
        <p className="text-gray-500">Create a message of appreciation with an optional gift.</p>
      </div>

      {error && (
        <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          {error}
        </div>
      )}

      <FlawagramForm onSubmit={handleSubmit} isSubmitting={isSubmitting} />
    </div>
  );
}