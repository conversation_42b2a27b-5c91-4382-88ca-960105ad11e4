import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { 
  SearchIcon, 
  HeartIcon, 
  TreePineIcon,
  GraduationCapIcon,
  StethoscopeIcon,
  UsersIcon,
  TrophyIcon,
  MapPinIcon,
  CalendarIcon,
  TrendingUpIcon
} from 'lucide-react';

interface CSRInitiative {
  id: string;
  title: string;
  description: string;
  companyName: string;
  industry: string;
  region: string;
  theme: 'education' | 'healthcare' | 'environment' | 'community' | 'other';
  impactMetrics: {
    recipientCount?: number;
    donationAmount?: number;
    volunteersInvolved?: number;
  };
  imageUrl?: string;
  isFeatured: boolean;
  createdAt: Date;
}

const mockInitiatives: CSRInitiative[] = [
  {
    id: '1',
    title: 'Tech for Education: Bridging the Digital Divide',
    description: 'Through our Flawagram impact gifts, we donated laptops and provided coding workshops to underserved schools, reaching over 200 students this quarter.',
    companyName: 'TechCorp Solutions',
    industry: 'Technology',
    region: 'North America',
    theme: 'education',
    impactMetrics: {
      recipientCount: 200,
      donationAmount: 15000,
      volunteersInvolved: 25,
    },
    isFeatured: true,
    createdAt: new Date('2024-01-20'),
  },
  {
    id: '2',
    title: 'Healthcare Heroes Support Program',
    description: 'Partnering with local hospitals to provide wellness packages and mental health resources to frontline healthcare workers during challenging times.',
    companyName: 'AcmeCo Industries',
    industry: 'Manufacturing',
    region: 'North America',
    theme: 'healthcare',
    impactMetrics: {
      recipientCount: 150,
      donationAmount: 8000,
      volunteersInvolved: 15,
    },
    isFeatured: true,
    createdAt: new Date('2024-01-18'),
  },
  {
    id: '3',
    title: 'Green Future Initiative: 1000 Trees Planted',
    description: 'Every impact gift we send plants trees in partnership with environmental organizations. This quarter, we reached our goal of 1000 trees planted!',
    companyName: 'GreenTech Solutions',
    industry: 'Technology',
    region: 'North America',
    theme: 'environment',
    impactMetrics: {
      recipientCount: 500,
      donationAmount: 5000,
      volunteersInvolved: 30,
    },
    isFeatured: false,
    createdAt: new Date('2024-01-15'),
  },
  {
    id: '4',
    title: 'Community Kitchen Support Network',
    description: 'Supporting local food banks and community kitchens through meal donations, helping feed families in need across our operating regions.',
    companyName: 'Startup Inc',
    industry: 'Technology',
    region: 'North America',
    theme: 'community',
    impactMetrics: {
      recipientCount: 300,
      donationAmount: 12000,
      volunteersInvolved: 20,
    },
    isFeatured: false,
    createdAt: new Date('2024-01-12'),
  },
];

export function CSRPage() {
  const [initiatives, setInitiatives] = useState<CSRInitiative[]>(mockInitiatives);
  const [searchTerm, setSearchTerm] = useState('');
  const [themeFilter, setThemeFilter] = useState<string>('all');
  const [regionFilter, setRegionFilter] = useState<string>('all');

  const filteredInitiatives = initiatives.filter(initiative => {
    const matchesSearch = initiative.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         initiative.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         initiative.companyName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesTheme = themeFilter === 'all' || initiative.theme === themeFilter;
    const matchesRegion = regionFilter === 'all' || initiative.region === regionFilter;
    
    return matchesSearch && matchesTheme && matchesRegion;
  });

  const getThemeIcon = (theme: string) => {
    switch (theme) {
      case 'education':
        return <GraduationCapIcon className="h-5 w-5" />;
      case 'healthcare':
        return <StethoscopeIcon className="h-5 w-5" />;
      case 'environment':
        return <TreePineIcon className="h-5 w-5" />;
      case 'community':
        return <UsersIcon className="h-5 w-5" />;
      default:
        return <HeartIcon className="h-5 w-5" />;
    }
  };

  const getThemeBadge = (theme: string) => {
    const badges = {
      education: 'bg-blue-100 text-blue-800',
      healthcare: 'bg-red-100 text-red-800',
      environment: 'bg-green-100 text-green-800',
      community: 'bg-purple-100 text-purple-800',
      other: 'bg-gray-100 text-gray-800',
    };
    
    return `px-3 py-1 text-sm font-medium rounded-full ${badges[theme as keyof typeof badges] || 'bg-gray-100 text-gray-800'}`;
  };

  // Calculate total impact
  const totalImpact = initiatives.reduce((acc, initiative) => ({
    recipients: acc.recipients + (initiative.impactMetrics.recipientCount || 0),
    donations: acc.donations + (initiative.impactMetrics.donationAmount || 0),
    volunteers: acc.volunteers + (initiative.impactMetrics.volunteersInvolved || 0),
  }), { recipients: 0, donations: 0, volunteers: 0 });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight mb-4">Corporate Social Responsibility</h1>
            <p className="text-lg text-gray-600 mb-6">
              Showcasing how companies use Flawagram to create meaningful impact in their communities
            </p>
            
            {/* Impact Stats */}
            <div className="flex justify-center space-x-8 text-center">
              <div>
                <p className="text-2xl font-bold text-black">{totalImpact.recipients.toLocaleString()}+</p>
                <p className="text-sm text-gray-500">Lives Impacted</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-black">${totalImpact.donations.toLocaleString()}+</p>
                <p className="text-sm text-gray-500">Donated</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-black">{totalImpact.volunteers}+</p>
                <p className="text-sm text-gray-500">Volunteers</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search initiatives, companies, or impact areas..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={themeFilter}
                  onChange={(e) => setThemeFilter(e.target.value)}
                  className="h-10 rounded-md border border-gray-200 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2"
                >
                  <option value="all">All Themes</option>
                  <option value="education">Education</option>
                  <option value="healthcare">Healthcare</option>
                  <option value="environment">Environment</option>
                  <option value="community">Community</option>
                  <option value="other">Other</option>
                </select>
                <select
                  value={regionFilter}
                  onChange={(e) => setRegionFilter(e.target.value)}
                  className="h-10 rounded-md border border-gray-200 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2"
                >
                  <option value="all">All Regions</option>
                  <option value="North America">North America</option>
                  <option value="Europe">Europe</option>
                  <option value="Asia Pacific">Asia Pacific</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Featured Initiatives */}
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-4 flex items-center">
            <TrophyIcon className="h-5 w-5 mr-2 text-yellow-500" />
            Featured Initiatives
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredInitiatives.filter(initiative => initiative.isFeatured).map((initiative) => (
              <Card key={initiative.id} className="border-2 border-yellow-200 bg-yellow-50">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {getThemeIcon(initiative.theme)}
                      <span className={getThemeBadge(initiative.theme)}>
                        {initiative.theme}
                      </span>
                    </div>
                    <TrophyIcon className="h-5 w-5 text-yellow-500" />
                  </div>

                  <h3 className="font-semibold text-lg mb-2">{initiative.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{initiative.description}</p>

                  <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                    <div>
                      <p className="text-lg font-bold text-black">{initiative.impactMetrics.recipientCount}</p>
                      <p className="text-xs text-gray-500">Recipients</p>
                    </div>
                    <div>
                      <p className="text-lg font-bold text-black">${initiative.impactMetrics.donationAmount?.toLocaleString()}</p>
                      <p className="text-xs text-gray-500">Donated</p>
                    </div>
                    <div>
                      <p className="text-lg font-bold text-black">{initiative.impactMetrics.volunteersInvolved}</p>
                      <p className="text-xs text-gray-500">Volunteers</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{initiative.companyName} • {initiative.industry}</span>
                    <span>{initiative.createdAt.toLocaleDateString()}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* All Initiatives */}
        <div>
          <h2 className="text-xl font-bold mb-4">All CSR Initiatives</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredInitiatives.filter(initiative => !initiative.isFeatured).map((initiative) => (
              <Card key={initiative.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {getThemeIcon(initiative.theme)}
                      <span className={getThemeBadge(initiative.theme)}>
                        {initiative.theme}
                      </span>
                    </div>
                  </div>

                  <h3 className="font-semibold text-lg mb-2">{initiative.title}</h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">{initiative.description}</p>

                  <div className="grid grid-cols-2 gap-4 mb-4 text-center">
                    <div>
                      <p className="text-sm font-bold text-black">{initiative.impactMetrics.recipientCount}</p>
                      <p className="text-xs text-gray-500">Recipients</p>
                    </div>
                    <div>
                      <p className="text-sm font-bold text-black">${initiative.impactMetrics.donationAmount?.toLocaleString()}</p>
                      <p className="text-xs text-gray-500">Donated</p>
                    </div>
                  </div>

                  <div className="space-y-1 text-xs text-gray-500">
                    <div className="flex items-center space-x-2">
                      <UsersIcon className="h-3 w-3" />
                      <span>{initiative.companyName}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPinIcon className="h-3 w-3" />
                      <span>{initiative.region}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CalendarIcon className="h-3 w-3" />
                      <span>{initiative.createdAt.toLocaleDateString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <Card className="mt-12 bg-black text-white">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Share Your CSR Impact</h2>
            <p className="text-gray-300 mb-6">
              Making a difference through Flawagram? We'd love to showcase your company's social impact initiatives.
            </p>
            <Button variant="outline" className="bg-white text-black hover:bg-gray-100">
              Submit Your Initiative
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
