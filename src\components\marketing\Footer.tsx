import React from 'react';
import { Link } from 'react-router-dom';
import { FlowerIcon } from 'lucide-react';

export function Footer() {
  return (
    <footer className="border-t bg-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          <div className="flex flex-col">
            <Link to="/" className="flex items-center mb-4">
              <FlowerIcon className="h-6 w-6 mr-2" />
              <span className="text-xl font-semibold">Flawagram</span>
            </Link>
            <p className="text-sm text-gray-500 mb-4">
              The beautiful way to recognize and appreciate businesses and teams.
            </p>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold mb-4">Product</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/how-it-works" className="text-sm text-gray-500 hover:text-black">
                  How It Works
                </Link>
              </li>
              <li>
                <Link to="/pricing" className="text-sm text-gray-500 hover:text-black">
                  Pricing
                </Link>
              </li>
              <li>
                <Link to="/" className="text-sm text-gray-500 hover:text-black">
                  Templates
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold mb-4">Company</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/about" className="text-sm text-gray-500 hover:text-black">
                  About
                </Link>
              </li>
              <li>
                <Link to="/" className="text-sm text-gray-500 hover:text-black">
                  Blog
                </Link>
              </li>
              <li>
                <Link to="/" className="text-sm text-gray-500 hover:text-black">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold mb-4">Legal</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="text-sm text-gray-500 hover:text-black">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/" className="text-sm text-gray-500 hover:text-black">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-12 border-t pt-8">
          <p className="text-center text-sm text-gray-500">
            &copy; {new Date().getFullYear()} Flawagram. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}