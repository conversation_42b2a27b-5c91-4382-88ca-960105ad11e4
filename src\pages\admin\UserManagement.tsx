import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>eader, Card<PERSON>itle, CardContent } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { 
  SearchIcon, 
  FilterIcon, 
  MoreVerticalIcon,
  UserIcon,
  BuildingIcon,
  MailIcon,
  CalendarIcon
} from 'lucide-react';

interface User {
  id: string;
  name: string;
  email: string;
  company: string;
  role: string;
  status: 'active' | 'inactive' | 'suspended';
  joinedAt: Date;
  lastActive: Date;
  flawagramsSent: number;
  flawagramsReceived: number;
}

const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    company: 'TechCorp Solutions',
    role: 'Admin',
    status: 'active',
    joinedAt: new Date('2024-01-15'),
    lastActive: new Date('2024-01-25'),
    flawagramsSent: 45,
    flawagramsReceived: 23,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    company: 'AcmeCo Industries',
    role: 'Admin',
    status: 'active',
    joinedAt: new Date('2024-02-01'),
    lastActive: new Date('2024-01-24'),
    flawagramsSent: 32,
    flawagramsReceived: 18,
  },
  {
    id: '3',
    name: 'Mike Chen',
    email: '<EMAIL>',
    company: 'GreenTech Solutions',
    role: 'Member',
    status: 'active',
    joinedAt: new Date('2024-01-20'),
    lastActive: new Date('2024-01-23'),
    flawagramsSent: 12,
    flawagramsReceived: 8,
  },
  {
    id: '4',
    name: 'Emily Davis',
    email: '<EMAIL>',
    company: 'Startup Inc',
    role: 'Admin',
    status: 'inactive',
    joinedAt: new Date('2024-01-10'),
    lastActive: new Date('2024-01-15'),
    flawagramsSent: 5,
    flawagramsReceived: 3,
  },
];

export function UserManagement() {
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'suspended'>('all');

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.company.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'inactive':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      case 'suspended':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const handleStatusChange = (userId: string, newStatus: 'active' | 'inactive' | 'suspended') => {
    setUsers(users.map(user => 
      user.id === userId ? { ...user, status: newStatus } : user
    ));
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">User Management</h1>
        <p className="text-gray-500">Manage platform users and their access</p>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search users, emails, or companies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="h-10 rounded-md border border-gray-200 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
              </select>
              <Button variant="outline">
                <FilterIcon className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">
            Platform Users ({filteredUsers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-500">User</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Company</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Role</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Activity</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <UserIcon className="h-4 w-4 text-gray-500" />
                        </div>
                        <div>
                          <p className="font-medium">{user.name}</p>
                          <p className="text-sm text-gray-500">{user.email}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <BuildingIcon className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{user.company}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className="text-sm">{user.role}</span>
                    </td>
                    <td className="py-4 px-4">
                      <span className={getStatusBadge(user.status)}>
                        {user.status}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm">
                        <p>Sent: {user.flawagramsSent}</p>
                        <p className="text-gray-500">Received: {user.flawagramsReceived}</p>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <MailIcon className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MoreVerticalIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
