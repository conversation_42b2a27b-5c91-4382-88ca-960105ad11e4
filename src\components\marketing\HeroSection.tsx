import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '../ui/Button';
import { ArrowRightIcon } from 'lucide-react';

export function HeroSection() {
  return (
    <section className="py-20 md:py-32 bg-white">
      <div className="container mx-auto px-4 text-center">
        <motion.h1 
          className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Business recognition
          <br />
          <span className="text-gray-800">that builds relationships</span>
        </motion.h1>
        
        <motion.p 
          className="mx-auto max-w-lg text-lg text-gray-500 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          Send thoughtful, branded messages and gifts to clients, partners, and employees.
          Build stronger business relationships through meaningful appreciation.
        </motion.p>
        
        <motion.div
          className="flex flex-col sm:flex-row justify-center gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Link to="/register">
            <Button size="lg" className="w-full sm:w-auto">
              Get Started
              <ArrowRightIcon className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          <Link to="/how-it-works">
            <Button variant="outline" size="lg" className="w-full sm:w-auto">
              See How It Works
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}