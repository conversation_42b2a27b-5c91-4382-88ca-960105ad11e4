import React from 'react';
import { 
  SendIcon, 
  UsersIcon, 
  BarChart3Icon, 
  HeartIcon 
} from 'lucide-react';

const features = [
  {
    title: 'Easy to Send',
    description: 'Create and send Flawagrams in minutes with our simple, intuitive interface.',
    icon: SendIcon,
  },
  {
    title: 'Team Collaboration',
    description: 'Manage your company account with role-based permissions for your entire team.',
    icon: UsersIcon,
  },
  {
    title: 'Engagement Analytics',
    description: 'Track the impact of your recognition efforts with detailed analytics.',
    icon: BarChart3Icon,
  },
  {
    title: 'Meaningful Recognition',
    description: 'Build stronger relationships through thoughtful, personalized appreciation.',
    icon: HeartIcon,
  },
];

export function FeaturesSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Why Choose Flawagram</h2>
          <p className="text-gray-500 max-w-2xl mx-auto">
            Designed specifically for business recognition needs, Flawagram helps you build stronger relationships through meaningful appreciation.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
              <div className="inline-flex items-center justify-center p-2 bg-gray-100 rounded-lg mb-4">
                <feature.icon className="h-6 w-6" />
              </div>
              <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
              <p className="text-gray-500">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}