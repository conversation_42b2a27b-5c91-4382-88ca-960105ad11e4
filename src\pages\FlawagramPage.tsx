import React, { useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Textarea } from '../components/ui/Textarea';
import {
  FlowerIcon,
  CheckCircleIcon,
  HeartIcon,
  SendIcon,
  ArrowRightIcon
} from 'lucide-react';
import { useData } from '../contexts/DataContext';

export function FlawagramPage() {
  const [showResponse, setShowResponse] = useState(false);
  const { id } = useParams<{ id: string }>();
  const { getFlawagramById } = useData();

  const flawagram = id ? getFlawagramById(id) : null;

  if (!flawagram) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4">
        <div className="container mx-auto max-w-md text-center">
          <Link to="/" className="inline-flex items-center mb-8">
            <FlowerIcon className="h-6 w-6 mr-2" />
            <span className="text-xl font-semibold">Flawagram</span>
          </Link>
          <Card className="shadow-lg">
            <CardContent className="p-6">
              <h2 className="text-xl font-bold mb-4">Flawagram Not Found</h2>
              <p className="text-gray-500 mb-6">
                This Flawagram may have been removed or the link is invalid.
              </p>
              <Link to="/">
                <Button>Go Home</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="container mx-auto max-w-md">
        <Link to="/" className="inline-flex items-center mb-8">
          <FlowerIcon className="h-6 w-6 mr-2" />
          <span className="text-xl font-semibold">Flawagram</span>
        </Link>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-gray-200 mr-3 flex items-center justify-center">
                    <FlowerIcon className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold">{flawagram.senderCompany.name}</h3>
                    <p className="text-xs text-gray-500">
                      {flawagram.createdAt.toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="bg-black text-white text-xs px-2 py-1 rounded">
                  Flawagram
                </div>
              </div>

              <div className="border-l-4 border-gray-200 pl-4 mb-6">
                <p className="text-gray-700 italic">
                  "{flawagram.message}"
                </p>
              </div>

              {flawagram.gift && (
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <p className="font-medium">{flawagram.gift.name}</p>
                    <p className="font-semibold">${flawagram.gift.price}</p>
                  </div>
                  <p className="text-xs text-gray-500">{flawagram.gift.description}</p>
                </div>
              )}
              
              {!showResponse ? (
                <div className="space-y-4">
                  <Button 
                    className="w-full" 
                    onClick={() => setShowResponse(true)}
                  >
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    Accept Gift & Respond
                  </Button>
                  
                  <div className="text-center">
                    <Button variant="ghost" className="text-sm">
                      <HeartIcon className="h-4 w-4 mr-2" />
                      Just Say Thanks
                    </Button>
                  </div>
                </div>
              ) : (
                <motion.div 
                  className="space-y-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <p className="text-sm font-medium mb-2">Your Response</p>
                  <Textarea 
                    placeholder="Write your response here..."
                    className="w-full"
                    rows={4}
                  />
                  
                  <div className="flex gap-3">
                    <Button className="flex-1">
                      <SendIcon className="h-4 w-4 mr-2" />
                      Send Response
                    </Button>
                    <Button variant="outline" className="flex-1">
                      Create Account
                      <ArrowRightIcon className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </motion.div>
              )}
            </CardContent>
          </Card>
          
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500 mb-4">
              Want to send your own Flawagrams?
            </p>
            <Link to="/register">
              <Button variant="outline">
                Create Your Account
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
}