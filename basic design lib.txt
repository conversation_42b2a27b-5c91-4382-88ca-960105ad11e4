Here is a **basic design library** for your minimalist app using the **ShadCN UI system**, focused strictly on **black, white, and greys**, a clean layout, 8px radii for buttons/cards, thoughtful font sizing, and generous whitespace.

---

### 🧱 Minimalist ShadCN UI Design Tokens (JSON)

```json
{
  "theme": {
    "colors": {
      "background": "#ffffff",
      "foreground": "#000000",
      "muted": "#f5f5f5",
      "mutedForeground": "#6e6e6e",
      "border": "#e0e0e0",
      "card": "#ffffff",
      "cardForeground": "#000000",
      "button": {
        "default": "#000000",
        "hover": "#1a1a1a",
        "text": "#ffffff"
      }
    },
    "borderRadius": {
      "button": "8px",
      "card": "8px",
      "input": "6px"
    },
    "spacing": {
      "xs": "4px",
      "sm": "8px",
      "md": "16px",
      "lg": "24px",
      "xl": "32px"
    },
    "typography": {
      "fontFamily": "'Inter', sans-serif",
      "heading": {
        "h1": {
          "fontSize": "32px",
          "fontWeight": "600",
          "lineHeight": "40px"
        },
        "h2": {
          "fontSize": "24px",
          "fontWeight": "600",
          "lineHeight": "32px"
        },
        "h3": {
          "fontSize": "18px",
          "fontWeight": "500",
          "lineHeight": "28px"
        }
      },
      "body": {
        "lg": {
          "fontSize": "16px",
          "lineHeight": "24px",
          "fontWeight": "400"
        },
        "md": {
          "fontSize": "14px",
          "lineHeight": "20px",
          "fontWeight": "400"
        },
        "sm": {
          "fontSize": "12px",
          "lineHeight": "18px",
          "fontWeight": "400"
        }
      }
    },
    "components": {
      "button": {
        "borderRadius": "8px",
        "padding": "12px 20px",
        "fontSize": "14px",
        "fontWeight": "500",
        "background": "#000000",
        "textColor": "#ffffff",
        "hover": "#1a1a1a"
      },
      "card": {
        "borderRadius": "8px",
        "padding": "24px",
        "background": "#ffffff",
        "boxShadow": "0 1px 2px rgba(0, 0, 0, 0.05)",
        "border": "#e0e0e0"
      },
      "input": {
        "borderRadius": "6px",
        "padding": "10px 14px",
        "border": "#e0e0e0",
        "background": "#ffffff",
        "textColor": "#000000"
      },
      "label": {
        "fontSize": "12px",
        "fontWeight": "500",
        "color": "#6e6e6e"
      }
    }
  }
}
```

---

### ✅ Design Notes

* **Colors**: Only black, white, and muted greys. Very clean.
* **Typography**: Balanced hierarchy with ample spacing.
* **Radius**: Uniform `8px` for all major components.
* **Whitespace**: Built-in via spacing tokens (`md`, `lg`, etc.).
* **Perfect for ShadCN + Tailwind**: This JSON structure is easily translated to Tailwind config or theming logic in ShadCN UI.

Let me know if you want the Tailwind `theme.extend` version or a Figma token sheet.