import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '../components/ui/Button';
import { HowItWorksSection } from '../components/marketing/HowItWorksSection';
import {
  FlowerIcon,
  ArrowRightIcon,
  SendIcon,
  HeartIcon,
  UsersIcon
} from 'lucide-react';

export function HowItWorksPage() {
  return (
    <div>
      {/* Hero Section */}
      <section className="py-20 md:py-32 bg-white">
        <div className="container mx-auto px-4 text-center">
          <motion.h1
            className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            How Flawagram Works
          </motion.h1>

          <motion.p
            className="text-xl text-gray-500 max-w-3xl mx-auto mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            Send meaningful business recognition in just a few simple steps.
            Build stronger relationships through thoughtful appreciation.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Link to="/register">
              <Button size="lg">
                Get Started
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Use existing HowItWorksSection component */}
      <HowItWorksSection />

      {/* Additional Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Businesses Choose Flawagram</h2>
            <p className="text-gray-500 max-w-2xl mx-auto">
              More than just a thank you note - Flawagram helps you build lasting business relationships
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <SendIcon className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Professional Presentation</h3>
              <p className="text-gray-500">
                Every Flawagram is beautifully designed to reflect your brand and make a lasting impression.
              </p>
            </motion.div>

            <motion.div
              className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <HeartIcon className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Meaningful Impact</h3>
              <p className="text-gray-500">
                Recipients can respond and engage, creating genuine two-way business relationships.
              </p>
            </motion.div>

            <motion.div
              className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <UsersIcon className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Team Collaboration</h3>
              <p className="text-gray-500">
                Manage your company account with role-based permissions for your entire team.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-black text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <FlowerIcon className="h-12 w-12 mx-auto mb-6" />

            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to start building better relationships?
            </h2>

            <p className="max-w-2xl mx-auto text-gray-300 mb-8">
              Join companies using Flawagram to show meaningful appreciation and strengthen business connections.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register">
                <Button size="lg" className="bg-white text-black hover:bg-gray-100 w-full sm:w-auto">
                  Get Started
                </Button>
              </Link>
              <Link to="/pricing">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10 w-full sm:w-auto">
                  View Pricing
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
