import React from 'react';
import { Card, CardContent } from '../ui/Card';
import { SendIcon, PackageIcon, HeartIcon, BarChart3Icon } from 'lucide-react';

interface Stat {
  label: string;
  value: string | number;
  icon: React.ElementType;
  change?: {
    value: string;
    positive: boolean;
  };
}

interface DashboardStatsProps {
  stats: Stat[];
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <div className="flex items-start">
              <div className="mr-4 bg-gray-100 p-2 rounded-md">
                <stat.icon className="h-5 w-5 text-gray-700" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">{stat.label}</p>
                <p className="text-2xl font-bold">{stat.value}</p>
                {stat.change && (
                  <p className={`text-xs ${stat.change.positive ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change.positive ? '↑' : '↓'} {stat.change.value}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export function getDefaultStats() {
  return [
    {
      label: 'Flawagrams Sent',
      value: 25,
      icon: SendIcon,
      change: {
        value: '12% from last month',
        positive: true,
      },
    },
    {
      label: 'Flawagrams Received',
      value: 18,
      icon: PackageIcon,
      change: {
        value: '5% from last month',
        positive: true,
      },
    },
    {
      label: 'Response Rate',
      value: '84%',
      icon: HeartIcon,
      change: {
        value: '2% from last month',
        positive: true,
      },
    },
    {
      label: 'Engagement Score',
      value: 92,
      icon: BarChart3Icon,
      change: {
        value: '3% from last month',
        positive: true,
      },
    },
  ];
}