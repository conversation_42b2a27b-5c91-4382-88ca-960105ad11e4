import React, { createContext, useContext, useState, useEffect } from 'react';
import { Flawagram, Company, Gift, FlawagramFormData } from '../types';
import { useAuth } from './AuthContext';

interface DataContextType {
  flawagrams: Flawagram[];
  companies: Company[];
  gifts: Gift[];
  isLoading: boolean;
  sendFlawagram: (data: FlawagramFormData) => Promise<void>;
  getFlawagramById: (id: string) => Flawagram | undefined;
  searchCompanies: (query: string) => Company[];
  refreshData: () => Promise<void>;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export function useData() {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}

// Mock data
const mockCompanies: Company[] = [
  {
    id: 'company1',
    name: 'TechCorp Solutions',
    logoUrl: '',
    industry: 'Technology',
    size: '50-100',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'company2',
    name: 'AcmeCo Industries',
    logoUrl: '',
    industry: 'Manufacturing',
    size: '100-500',
    createdAt: new Date('2024-02-01'),
  },
  {
    id: 'company3',
    name: 'Creative Studio',
    logoUrl: '',
    industry: 'Design',
    size: '10-50',
    createdAt: new Date('2024-01-20'),
  },
  {
    id: 'company4',
    name: 'Global Consulting',
    logoUrl: '',
    industry: 'Consulting',
    size: '500+',
    createdAt: new Date('2024-01-10'),
  },
];

const mockGifts: Gift[] = [
  // Branded Bundles
  {
    id: 'gift1',
    name: 'Custom Company Mug Set',
    description: 'Premium ceramic mugs with custom company branding',
    imageUrl: 'https://images.unsplash.com/photo-**********-641a0ac8b55e?w=300&h=200&fit=crop',
    price: 35,
    category: 'branded_bundles',
    subcategory: 'drinkware',
    isAvailable: true,
    inventory: 50,
    tags: ['customizable', 'corporate'],
    isCustomizable: true,
  },
  {
    id: 'gift2',
    name: 'Branded Notebook & Pen Set',
    description: 'High-quality leather notebook with matching pen',
    imageUrl: 'https://images.unsplash.com/photo-*************-21bda4d32df4?w=300&h=200&fit=crop',
    price: 45,
    category: 'branded_bundles',
    subcategory: 'stationery',
    isAvailable: true,
    inventory: 30,
    tags: ['professional', 'customizable'],
    isCustomizable: true,
  },

  // Edibles
  {
    id: 'gift3',
    name: 'Artisanal Chocolate Box',
    description: 'Premium handcrafted chocolates from local chocolatiers',
    imageUrl: 'https://images.unsplash.com/photo-*************-cd288d649433?w=300&h=200&fit=crop',
    price: 40,
    category: 'edibles',
    subcategory: 'chocolate',
    isAvailable: true,
    inventory: 25,
    tags: ['premium', 'local'],
  },
  {
    id: 'gift4',
    name: 'Gourmet Coffee Blend',
    description: 'Single-origin coffee beans from sustainable farms',
    imageUrl: 'https://images.unsplash.com/photo-**********-641a0ac8b55e?w=300&h=200&fit=crop',
    price: 30,
    category: 'edibles',
    subcategory: 'beverages',
    isAvailable: true,
    inventory: 40,
    tags: ['sustainable', 'premium'],
  },

  // Wellness
  {
    id: 'gift5',
    name: 'Aromatherapy Candle Set',
    description: 'Hand-poured soy candles with essential oils',
    imageUrl: 'https://images.unsplash.com/photo-*************-21bda4d32df4?w=300&h=200&fit=crop',
    price: 35,
    category: 'wellness',
    subcategory: 'aromatherapy',
    isAvailable: true,
    inventory: 20,
    tags: ['relaxation', 'natural'],
  },
  {
    id: 'gift6',
    name: 'Spa Gift Set',
    description: 'Luxurious bath salts, oils, and wellness essentials',
    imageUrl: 'https://images.unsplash.com/photo-*************-cd288d649433?w=300&h=200&fit=crop',
    price: 55,
    category: 'wellness',
    subcategory: 'self-care',
    isAvailable: true,
    inventory: 15,
    tags: ['luxury', 'self-care'],
  },

  // Impact Gifts
  {
    id: 'gift7',
    name: 'Tree Planting Initiative',
    description: 'Plant 5 trees in recipient\'s name through our environmental partner',
    imageUrl: 'https://images.unsplash.com/photo-*************-cd288d649433?w=300&h=200&fit=crop',
    price: 25,
    category: 'impact_gifts',
    subcategory: 'environment',
    isAvailable: true,
    inventory: 100,
    tags: ['environmental', 'sustainable', 'meaningful'],
  },
  {
    id: 'gift8',
    name: 'Meal Donation',
    description: 'Provide 10 meals to local food bank in recipient\'s honor',
    imageUrl: 'https://images.unsplash.com/photo-*************-cd288d649433?w=300&h=200&fit=crop',
    price: 30,
    category: 'impact_gifts',
    subcategory: 'community',
    isAvailable: true,
    inventory: 200,
    tags: ['community', 'charity', 'meaningful'],
  },

  // Experiences
  {
    id: 'gift9',
    name: 'Virtual Cooking Class',
    description: 'Interactive cooking session with professional chef',
    imageUrl: 'https://images.unsplash.com/photo-*************-21bda4d32df4?w=300&h=200&fit=crop',
    price: 75,
    category: 'experiences',
    subcategory: 'virtual',
    isAvailable: true,
    inventory: 10,
    tags: ['interactive', 'skill-building'],
  },
  {
    id: 'gift10',
    name: 'Wellness Workshop Ticket',
    description: 'Access to mindfulness and stress management workshop',
    imageUrl: 'https://images.unsplash.com/photo-*************-cd288d649433?w=300&h=200&fit=crop',
    price: 60,
    category: 'experiences',
    subcategory: 'wellness',
    isAvailable: true,
    inventory: 20,
    tags: ['wellness', 'professional-development'],
  },

  // Digital
  {
    id: 'gift11',
    name: 'Amazon Gift Card',
    description: 'Digital gift card for online shopping',
    imageUrl: 'https://images.unsplash.com/photo-**********-641a0ac8b55e?w=300&h=200&fit=crop',
    price: 50,
    category: 'digital',
    subcategory: 'gift-cards',
    isAvailable: true,
    inventory: 1000,
    tags: ['flexible', 'instant'],
  },
  {
    id: 'gift12',
    name: 'Online Course Access',
    description: 'Premium access to professional development courses',
    imageUrl: 'https://images.unsplash.com/photo-*************-21bda4d32df4?w=300&h=200&fit=crop',
    price: 80,
    category: 'digital',
    subcategory: 'education',
    isAvailable: true,
    inventory: 50,
    tags: ['education', 'professional-development'],
  },
];

const mockFlawagrams: Flawagram[] = [
  {
    id: 'flawagram1',
    message: 'Thank you for your excellent work on the recent project. Your team\'s attention to detail was outstanding!',
    gifts: [
      { gift: mockGifts[0], quantity: 2 },
      { gift: mockGifts[2], quantity: 1 }
    ],
    senderId: 'user1',
    senderCompanyId: 'company1',
    senderCompany: mockCompanies[0],
    recipientType: 'external',
    recipientId: 'user2',
    recipientCompanyId: 'company2',
    recipientCompany: mockCompanies[1],
    status: 'delivered',
    createdAt: new Date('2024-01-20'),
    totalValue: 110, // 2 * 35 + 1 * 40
  },
  {
    id: 'flawagram2',
    message: 'We really appreciate the partnership and look forward to working together on future projects.',
    gifts: [
      { gift: mockGifts[1], quantity: 1 }
    ],
    senderId: 'user2',
    senderCompanyId: 'company2',
    senderCompany: mockCompanies[1],
    recipientType: 'external',
    recipientId: 'user1',
    recipientCompanyId: 'company1',
    recipientCompany: mockCompanies[0],
    status: 'viewed',
    createdAt: new Date('2024-01-22'),
    totalValue: 45,
  },
  {
    id: 'flawagram3',
    message: 'Great job on the quarterly presentation! Your insights were valuable.',
    gifts: [
      { gift: mockGifts[4], quantity: 1 }
    ],
    senderId: 'user1',
    senderCompanyId: 'company1',
    senderCompany: mockCompanies[0],
    recipientType: 'internal',
    recipientEmail: '<EMAIL>',
    recipientName: 'Team Member',
    status: 'sent',
    createdAt: new Date('2024-01-25'),
    totalValue: 35,
  },
];

export function DataProvider({ children }: { children: React.ReactNode }) {
  const [flawagrams, setFlawagrams] = useState<Flawagram[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [gifts, setGifts] = useState<Gift[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user, company } = useAuth();

  useEffect(() => {
    // Load initial data
    loadData();
  }, [user]);

  const loadData = async () => {
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    setCompanies(mockCompanies);
    setGifts(mockGifts);
    setFlawagrams(mockFlawagrams);
    
    setIsLoading(false);
  };

  const sendFlawagram = async (data: FlawagramFormData) => {
    if (!user || !company) {
      throw new Error('User must be authenticated to send Flawagrams');
    }

    setIsLoading(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Handle recipient based on type
    let recipientCompany: Company | undefined;
    if (data.recipientType === 'internal' && data.recipientCompanyId) {
      recipientCompany = companies.find(c => c.id === data.recipientCompanyId);
      if (!recipientCompany) {
        throw new Error('Recipient company not found');
      }
    }

    // Process selected gifts
    const selectedGifts = data.gifts.map(giftItem => {
      const gift = gifts.find(g => g.id === giftItem.giftId);
      if (!gift) {
        throw new Error(`Gift with ID ${giftItem.giftId} not found`);
      }
      return {
        gift,
        quantity: giftItem.quantity,
      };
    });

    // Calculate total value
    const totalValue = selectedGifts.reduce((total, item) =>
      total + (item.gift.price * item.quantity), 0
    );

    const newFlawagram: Flawagram = {
      id: `flawagram_${Date.now()}`,
      message: data.message,
      gifts: selectedGifts,
      senderId: user.id,
      senderCompanyId: company.id,
      senderCompany: company,
      recipientType: data.recipientType,
      recipientCompanyId: data.recipientCompanyId,
      recipientCompany: recipientCompany,
      recipientEmail: data.recipientEmail,
      recipientName: data.recipientName,
      status: data.scheduledDate ? 'scheduled' : 'sent',
      createdAt: new Date(),
      scheduledDate: data.scheduledDate,
      videoMessageUrl: data.videoMessageUrl,
      audioMessageUrl: data.audioMessageUrl,
      totalValue,
      isFromTemplate: data.isTemplate,
    };

    setFlawagrams(prev => [newFlawagram, ...prev]);
    setIsLoading(false);
  };

  const getFlawagramById = (id: string) => {
    return flawagrams.find(f => f.id === id);
  };

  const searchCompanies = (query: string) => {
    if (!query.trim()) return companies;
    
    return companies.filter(company =>
      company.name.toLowerCase().includes(query.toLowerCase()) ||
      (company.industry && company.industry.toLowerCase().includes(query.toLowerCase()))
    );
  };

  const refreshData = async () => {
    await loadData();
  };

  const value = {
    flawagrams,
    companies,
    gifts,
    isLoading,
    sendFlawagram,
    getFlawagramById,
    searchCompanies,
    refreshData,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
}
