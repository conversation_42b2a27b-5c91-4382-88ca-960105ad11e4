import React from 'react';
import { Outlet, <PERSON> } from 'react-router-dom';
import { FlowerIcon } from 'lucide-react';

export function AuthLayout() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
      <Link to="/" className="mb-8 flex items-center">
        <FlowerIcon className="h-8 w-8 mr-2" />
        <span className="text-2xl font-semibold">Flawagram</span>
      </Link>
      <div className="w-full max-w-md">
        <Outlet />
      </div>
      <p className="mt-8 text-center text-sm text-gray-500">
        &copy; {new Date().getFullYear()} Flawagram. All rights reserved.
      </p>
    </div>
  );
}