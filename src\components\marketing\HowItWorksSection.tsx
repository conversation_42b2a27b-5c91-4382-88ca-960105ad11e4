import React from 'react';
import { motion } from 'framer-motion';
import { 
  Users2Icon, 
  SendIcon, 
  GiftIcon, 
  HeartHandshakeIcon 
} from 'lucide-react';

const steps = [
  {
    title: 'Create your company profile',
    description: 'Set up your Flawagram account with your company details and branding.',
    icon: Users2Icon,
  },
  {
    title: 'Compose your message',
    description: 'Write a thoughtful message to show your appreciation.',
    icon: SendIcon,
  },
  {
    title: 'Add an optional gift',
    description: 'Choose from our selection of digital or physical gifts to accompany your message.',
    icon: GiftIcon,
  },
  {
    title: 'Build lasting relationships',
    description: 'Recipients can respond and even send their own Flawagrams back to you.',
    icon: HeartHandshakeIcon,
  },
];

export function HowItWorksSection() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">How Flawagram Works</h2>
          <p className="text-gray-500 max-w-2xl mx-auto">
            Send meaningful recognition in just a few simple steps.
          </p>
        </div>
        
        <div className="relative">
          {/* Connection line */}
          <div className="absolute left-1/2 top-0 bottom-0 w-px bg-gray-200 -translate-x-1/2 hidden md:block" />
          
          <div className="space-y-12 relative">
            {steps.map((step, index) => (
              <motion.div 
                key={index}
                className="md:grid md:grid-cols-2 md:gap-8 items-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className={`mb-8 md:mb-0 ${index % 2 === 0 ? 'md:order-1' : 'md:order-2'}`}>
                  <div className="bg-gray-100 rounded-lg p-8 h-full flex flex-col justify-center">
                    <div className="mx-auto md:mx-0 bg-white rounded-full w-12 h-12 flex items-center justify-center mb-4 shadow-sm">
                      <step.icon className="h-6 w-6" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2 text-center md:text-left">
                      {step.title}
                    </h3>
                    <p className="text-gray-500 text-center md:text-left">
                      {step.description}
                    </p>
                  </div>
                </div>
                
                {/* Circle on timeline */}
                <div className="hidden md:flex items-center justify-center relative z-10">
                  <div className={`absolute left-0 right-0 flex items-center justify-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                    <div className="bg-black text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}