import React from 'react';
import { Link } from 'react-router-dom';
import { HeroSection } from '../components/marketing/HeroSection';
import { FeaturesSection } from '../components/marketing/FeaturesSection';
import { HowItWorksSection } from '../components/marketing/HowItWorksSection';
import { CTASection } from '../components/marketing/CTASection';
import { Card, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { FlowerIcon, CheckCircleIcon } from 'lucide-react';

export function HomePage() {
  return (
    <div>
      <HeroSection />
      
      {/* Social Proof Section */}
      <section className="py-12 border-y">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8">
            <p className="text-gray-500 text-sm">Trusted by companies of all sizes</p>
            
            <div className="flex flex-wrap justify-center gap-8 md:gap-12 items-center">
              <div className="text-gray-400 font-semibold">ACME Inc.</div>
              <div className="text-gray-400 font-semibold">TechCorp</div>
              <div className="text-gray-400 font-semibold">GlobalBiz</div>
              <div className="text-gray-400 font-semibold">FutureTech</div>
            </div>
          </div>
        </div>
      </section>
      
      <FeaturesSection />
      
      {/* Example Flawagram Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">What Is a Flawagram?</h2>
            <p className="text-gray-500 max-w-2xl mx-auto">
              A Flawagram is a thoughtful message of appreciation combined with an optional gift,
              designed to strengthen business relationships.
            </p>
          </div>
          
          <div className="max-w-md mx-auto">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-gray-200 mr-3 flex items-center justify-center">
                    <FlowerIcon className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold">TechCorp</h3>
                    <p className="text-xs text-gray-500">May 15, 2025</p>
                  </div>
                </div>
                
                <p className="text-gray-700 mb-6">
                  Thank you for the incredible partnership over the past quarter. Your team's dedication and creativity have made a significant impact on our project's success!
                </p>
                
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <p className="font-medium">Digital Gift Card</p>
                    <p className="font-semibold">$50</p>
                  </div>
                  <p className="text-xs text-gray-500">Redeemable at recipient's choice of stores</p>
                </div>
                
                <div className="text-center">
                  <Button className="w-full">
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    Accept Gift
                  </Button>
                  <p className="text-xs text-gray-500 mt-2">
                    Send a response and create your own Flawagram
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      
      <HowItWorksSection />
      
      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">What Our Users Say</h2>
            <p className="text-gray-500 max-w-2xl mx-auto">
              See how companies are using Flawagram to build stronger business relationships.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-gray-200 mr-3"></div>
                    <div>
                      <h3 className="font-semibold">Sarah Johnson</h3>
                      <p className="text-xs text-gray-500">Client Success Manager</p>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 mb-4">
                    "Flawagram has transformed how we show appreciation to our clients. The response rate has been incredible, and it's helped us build stronger relationships."
                  </p>
                  
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg
                        key={star}
                        className="h-4 w-4 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      
      <CTASection />
    </div>
  );
}