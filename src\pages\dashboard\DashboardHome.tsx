import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '../../components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import { DashboardStats, getDefaultStats } from '../../components/dashboard/DashboardStats';
import { FlawagramCard } from '../../components/dashboard/FlawagramCard';
import { SendIcon, TrendingUpIcon } from 'lucide-react';
import { useData } from '../../contexts/DataContext';
import { useAuth } from '../../contexts/AuthContext';

export function DashboardHome() {
  const { flawagrams, isLoading } = useData();
  const { company, user } = useAuth();

  // Get recent received flawagrams
  const recentReceived = flawagrams
    .filter(f => f.recipientCompanyId === company?.id)
    .slice(0, 3);

  // Calculate stats
  const sentCount = flawagrams.filter(f => f.senderCompanyId === company?.id).length;
  const receivedCount = flawagrams.filter(f => f.recipientCompanyId === company?.id).length;

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-gray-500">Welcome back to your Flawagram dashboard.</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-gray-500">Welcome back, {user?.name}! Here's your Flawagram overview.</p>
        </div>
        <Link to="/dashboard/send">
          <Button>
            <SendIcon className="mr-2 h-4 w-4" />
            Send Flawagram
          </Button>
        </Link>
      </div>

      <DashboardStats stats={getDefaultStats()} />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-medium">
                Recent Flawagrams Received
              </CardTitle>
              <Link to="/dashboard/inbox" className="text-xs text-gray-500 hover:underline">
                View all
              </Link>
            </CardHeader>
            <CardContent>
              {recentReceived.length > 0 ? (
                <div className="space-y-4">
                  {recentReceived.map(flawagram => (
                    <FlawagramCard
                      key={flawagram.id}
                      flawagram={flawagram}
                      type="received"
                      onView={(id) => console.log('View flawagram', id)}
                      onRespond={(id) => console.log('Respond to flawagram', id)}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">No Flawagrams received yet</p>
                  <p className="text-sm text-gray-400">
                    Share your company profile to start receiving recognition
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="text-base font-medium">
                Activity Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-black"></div>
                  <p className="text-sm">{sentCount} Flawagrams sent</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-black"></div>
                  <p className="text-sm">{receivedCount} Flawagrams received</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-green-600"></div>
                  <p className="text-sm flex items-center">
                    Building relationships
                    <TrendingUpIcon className="h-3 w-3 ml-1 text-green-600" />
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-black"></div>
                  <p className="text-sm">Company: {company?.name}</p>
                </div>
                
                <hr className="my-2" />
                
                <div className="pt-2">
                  <p className="text-sm font-medium mb-2">Quick Actions</p>
                  <div className="space-y-2">
                    <Link to="/dashboard/send">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <SendIcon className="mr-2 h-4 w-4" />
                        Send a Flawagram
                      </Button>
                    </Link>
                    <Link to="/dashboard/team">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <SendIcon className="mr-2 h-4 w-4" />
                        Invite Team Member
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}