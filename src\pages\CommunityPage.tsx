import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { 
  SearchIcon, 
  HeartIcon, 
  TrendingUpIcon,
  UsersIcon,
  StarIcon,
  MapPinIcon,
  CalendarIcon,
  GiftIcon
} from 'lucide-react';

interface CommunityStory {
  id: string;
  title: string;
  description: string;
  companyName: string;
  type: 'gifting_milestone' | 'featured_sender' | 'testimonial' | 'csr_initiative';
  flawagramCount?: number;
  imageUrl?: string;
  location?: string;
  createdAt: Date;
  likes: number;
  isLiked: boolean;
}

const mockStories: CommunityStory[] = [
  {
    id: '1',
    title: 'TechCorp Celebrates 100 Flawagrams Milestone!',
    description: 'Our team has sent over 100 Flawagrams this quarter, spreading appreciation across 25 partner companies. The response has been incredible!',
    companyName: 'TechCorp Solutions',
    type: 'gifting_milestone',
    flawagramCount: 100,
    location: 'San Francisco, CA',
    createdAt: new Date('2024-01-20'),
    likes: 42,
    isLiked: false,
  },
  {
    id: '2',
    title: 'Heartwarming Response from Healthcare Heroes',
    description: 'We sent wellness packages to frontline healthcare workers and received the most touching thank-you messages. Gratitude truly multiplies when shared.',
    companyName: 'AcmeCo Industries',
    type: 'testimonial',
    flawagramCount: 50,
    location: 'New York, NY',
    createdAt: new Date('2024-01-18'),
    likes: 67,
    isLiked: true,
  },
  {
    id: '3',
    title: 'Supporting Local Food Banks Through Flawagram',
    description: 'This month, we chose impact gifts that donated 500 meals to local food banks. Our partners loved being part of something meaningful.',
    companyName: 'GreenTech Solutions',
    type: 'csr_initiative',
    flawagramCount: 25,
    location: 'Austin, TX',
    createdAt: new Date('2024-01-15'),
    likes: 89,
    isLiked: false,
  },
  {
    id: '4',
    title: 'Featured Sender: Building Bridges Through Appreciation',
    description: 'How one small startup used Flawagram to strengthen relationships with enterprise clients and grow their business through genuine appreciation.',
    companyName: 'Startup Inc',
    type: 'featured_sender',
    flawagramCount: 75,
    location: 'Seattle, WA',
    createdAt: new Date('2024-01-12'),
    likes: 34,
    isLiked: false,
  },
];

export function CommunityPage() {
  const [stories, setStories] = useState<CommunityStory[]>(mockStories);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  const filteredStories = stories.filter(story => {
    const matchesSearch = story.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         story.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         story.companyName.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || story.type === typeFilter;
    
    return matchesSearch && matchesType;
  });

  const toggleLike = (storyId: string) => {
    setStories(stories.map(story => 
      story.id === storyId 
        ? { 
            ...story, 
            isLiked: !story.isLiked,
            likes: story.isLiked ? story.likes - 1 : story.likes + 1
          }
        : story
    ));
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'gifting_milestone':
        return <TrendingUpIcon className="h-4 w-4" />;
      case 'featured_sender':
        return <StarIcon className="h-4 w-4" />;
      case 'testimonial':
        return <HeartIcon className="h-4 w-4" />;
      case 'csr_initiative':
        return <UsersIcon className="h-4 w-4" />;
      default:
        return <GiftIcon className="h-4 w-4" />;
    }
  };

  const getTypeBadge = (type: string) => {
    const badges = {
      gifting_milestone: 'bg-blue-100 text-blue-800',
      featured_sender: 'bg-yellow-100 text-yellow-800',
      testimonial: 'bg-pink-100 text-pink-800',
      csr_initiative: 'bg-green-100 text-green-800',
    };
    
    return `px-2 py-1 text-xs font-medium rounded-full ${badges[type as keyof typeof badges] || 'bg-gray-100 text-gray-800'}`;
  };

  const totalFlawagrams = stories.reduce((sum, story) => sum + (story.flawagramCount || 0), 0);
  const totalCompanies = new Set(stories.map(story => story.companyName)).size;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight mb-4">Community Stories</h1>
            <p className="text-lg text-gray-600 mb-6">
              Celebrating the power of appreciation and the stories behind every Flawagram
            </p>
            
            {/* Community Stats */}
            <div className="flex justify-center space-x-8 text-center">
              <div>
                <p className="text-2xl font-bold text-black">{totalFlawagrams}+</p>
                <p className="text-sm text-gray-500">Flawagrams Shared</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-black">{totalCompanies}+</p>
                <p className="text-sm text-gray-500">Companies Featured</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-black">12+</p>
                <p className="text-sm text-gray-500">Cities Reached</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search stories, companies, or locations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="h-10 rounded-md border border-gray-200 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2"
                >
                  <option value="all">All Stories</option>
                  <option value="gifting_milestone">Milestones</option>
                  <option value="featured_sender">Featured Senders</option>
                  <option value="testimonial">Testimonials</option>
                  <option value="csr_initiative">CSR Initiatives</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStories.map((story) => (
            <Card key={story.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(story.type)}
                    <span className={getTypeBadge(story.type)}>
                      {story.type.replace('_', ' ')}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleLike(story.id)}
                    className={story.isLiked ? 'text-red-500' : 'text-gray-400'}
                  >
                    <HeartIcon className={`h-4 w-4 ${story.isLiked ? 'fill-current' : ''}`} />
                    <span className="ml-1 text-sm">{story.likes}</span>
                  </Button>
                </div>

                <h3 className="font-semibold text-lg mb-2">{story.title}</h3>
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">{story.description}</p>

                <div className="space-y-2 text-xs text-gray-500">
                  <div className="flex items-center space-x-2">
                    <UsersIcon className="h-3 w-3" />
                    <span>{story.companyName}</span>
                  </div>
                  
                  {story.location && (
                    <div className="flex items-center space-x-2">
                      <MapPinIcon className="h-3 w-3" />
                      <span>{story.location}</span>
                    </div>
                  )}
                  
                  {story.flawagramCount && (
                    <div className="flex items-center space-x-2">
                      <GiftIcon className="h-3 w-3" />
                      <span>{story.flawagramCount} Flawagrams</span>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="h-3 w-3" />
                    <span>{story.createdAt.toLocaleDateString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <Card className="mt-12 bg-black text-white">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Share Your Story</h2>
            <p className="text-gray-300 mb-6">
              Have an inspiring Flawagram story to share? We'd love to feature your company's journey of appreciation.
            </p>
            <Button variant="outline" className="bg-white text-black hover:bg-gray-100">
              Submit Your Story
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
