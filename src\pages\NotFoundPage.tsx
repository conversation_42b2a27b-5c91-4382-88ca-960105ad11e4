import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '../components/ui/Button';
import { FlowerIcon, ArrowLeftIcon, HomeIcon } from 'lucide-react';

export function NotFoundPage() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center px-4">
      <div className="max-w-md w-full text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Logo */}
          <Link to="/" className="inline-flex items-center mb-8">
            <FlowerIcon className="h-8 w-8 mr-2" />
            <span className="text-2xl font-semibold">Flawagram</span>
          </Link>

          {/* 404 */}
          <div className="mb-8">
            <div className="text-8xl font-bold mb-4">404</div>
            <div className="w-16 h-1 bg-black mx-auto rounded-full"></div>
          </div>

          {/* Error Message */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">
              Page Not Found
            </h1>
            <p className="text-gray-500 mb-6">
              Sorry, the page you're looking for doesn't exist.
              It might have been moved or deleted.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={() => navigate(-1)}
                variant="outline"
                className="flex items-center justify-center"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Go Back
              </Button>

              <Link to="/">
                <Button className="flex items-center justify-center w-full sm:w-auto">
                  <HomeIcon className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </Link>
            </div>

            <div className="mt-8">
              <p className="text-sm text-gray-500 mb-4">
                Looking for something specific?
              </p>
              <div className="flex flex-wrap justify-center gap-4 text-sm">
                <Link to="/about" className="text-gray-700 hover:text-black">
                  About
                </Link>
                <Link to="/how-it-works" className="text-gray-700 hover:text-black">
                  How It Works
                </Link>
                <Link to="/pricing" className="text-gray-700 hover:text-black">
                  Pricing
                </Link>
                <Link to="/login" className="text-gray-700 hover:text-black">
                  Login
                </Link>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-12 text-xs text-gray-400">
            <p>&copy; {new Date().getFullYear()} Flawagram. All rights reserved.</p>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
