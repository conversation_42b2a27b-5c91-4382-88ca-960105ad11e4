import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { FlawagramFormData, Company, Gift } from '../../types';
import {
  SearchIcon,
  PlusIcon,
  MinusIcon,
  CheckIcon,
  CalendarIcon,
  VideoIcon,
  MicIcon,
  UsersIcon,
  BuildingIcon,
  SaveIcon
} from 'lucide-react';
import { useData } from '../../contexts/DataContext';

const formSchema = z.object({
  recipientType: z.enum(['internal', 'external']),
  recipientCompanyId: z.string().optional(),
  recipientEmail: z.string().email().optional(),
  recipientName: z.string().optional(),
  message: z.string().min(10, "Message must be at least 10 characters"),
  gifts: z.array(z.object({
    giftId: z.string(),
    quantity: z.number().min(1),
  })).min(1, "At least one gift is required"),
  scheduledDate: z.date().optional(),
  videoMessageUrl: z.string().optional(),
  audioMessageUrl: z.string().optional(),
  isTemplate: z.boolean().optional(),
  templateName: z.string().optional(),
}).refine((data) => {
  if (data.recipientType === 'external') {
    return data.recipientEmail && data.recipientName;
  }
  return data.recipientCompanyId;
}, {
  message: "External recipients require email and name",
  path: ["recipientEmail"],
});

interface FlawagramFormProps {
  onSubmit: (data: FlawagramFormData) => void;
  isSubmitting?: boolean;
}

export function FlawagramForm({ onSubmit, isSubmitting = false }: FlawagramFormProps) {
  const [recipientType, setRecipientType] = useState<'internal' | 'external'>('internal');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [showCompanyResults, setShowCompanyResults] = useState(false);
  const [selectedGifts, setSelectedGifts] = useState<Array<{ giftId: string; quantity: number }>>([]);
  const [showScheduler, setShowScheduler] = useState(false);
  const [showMediaUpload, setShowMediaUpload] = useState(false);
  const { companies, gifts, searchCompanies } = useData();

  const searchResults = searchCompanies(searchQuery);

  const { register, handleSubmit, formState: { errors }, setValue, watch } = useForm<FlawagramFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      recipientType: 'internal',
      message: '',
      gifts: [],
    }
  });

  const watchRecipientType = watch('recipientType');

  const handleFormSubmit = (data: FlawagramFormData) => {
    const formData: FlawagramFormData = {
      ...data,
      gifts: selectedGifts,
    };
    onSubmit(formData);
  };

  const handleCompanySelect = (company: Company) => {
    setSelectedCompany(company);
    setSearchQuery(company.name);
    setShowCompanyResults(false);
    setValue('recipientCompanyId', company.id);
  };

  const handleGiftAdd = (giftId: string) => {
    const existingGift = selectedGifts.find(g => g.giftId === giftId);
    if (existingGift) {
      setSelectedGifts(selectedGifts.map(g =>
        g.giftId === giftId ? { ...g, quantity: g.quantity + 1 } : g
      ));
    } else {
      setSelectedGifts([...selectedGifts, { giftId, quantity: 1 }]);
    }
  };

  const handleGiftQuantityChange = (giftId: string, quantity: number) => {
    if (quantity <= 0) {
      setSelectedGifts(selectedGifts.filter(g => g.giftId !== giftId));
    } else {
      setSelectedGifts(selectedGifts.map(g =>
        g.giftId === giftId ? { ...g, quantity } : g
      ));
    }
  };

  const getTotalValue = () => {
    return selectedGifts.reduce((total, item) => {
      const gift = gifts.find(g => g.id === item.giftId);
      return total + (gift ? gift.price * item.quantity : 0);
    }, 0);
  };
  
  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Send a Flawagram</CardTitle>
      </CardHeader>

      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <CardContent className="space-y-6">
          {/* Recipient Type Toggle */}
          <div className="space-y-4">
            <label className="block text-sm font-medium">Recipient Type</label>
            <div className="flex space-x-4">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  value="internal"
                  {...register('recipientType')}
                  onChange={(e) => {
                    setRecipientType(e.target.value as 'internal' | 'external');
                    setValue('recipientType', e.target.value as 'internal' | 'external');
                  }}
                  className="text-black focus:ring-black"
                />
                <UsersIcon className="h-4 w-4" />
                <span>Internal (Team/Employee)</span>
              </label>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  value="external"
                  {...register('recipientType')}
                  onChange={(e) => {
                    setRecipientType(e.target.value as 'internal' | 'external');
                    setValue('recipientType', e.target.value as 'internal' | 'external');
                  }}
                  className="text-black focus:ring-black"
                />
                <BuildingIcon className="h-4 w-4" />
                <span>External (Business Partner)</span>
              </label>
            </div>
          </div>

          {/* Recipient Selection */}
          {recipientType === 'internal' ? (
            <div className="space-y-2">
              <label htmlFor="recipient" className="block text-sm font-medium">
                Select Team Member or Department
              </label>
              <div className="relative">
                <Input
                  id="recipient"
                  placeholder="Search for team members or departments..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    setShowCompanyResults(true);
                  }}
                  onFocus={() => setShowCompanyResults(true)}
                />
                <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />

                {/* Company Search Results */}
                {showCompanyResults && searchQuery && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                    {searchResults.length > 0 ? (
                      searchResults.map((company) => (
                        <button
                          key={company.id}
                          type="button"
                          className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                          onClick={() => handleCompanySelect(company)}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-900">{company.name}</p>
                              {company.industry && (
                                <p className="text-sm text-gray-500">{company.industry}</p>
                              )}
                            </div>
                            {selectedCompany?.id === company.id && (
                              <CheckIcon className="h-4 w-4 text-green-600" />
                            )}
                          </div>
                        </button>
                      ))
                    ) : (
                      <div className="px-4 py-3 text-gray-500 text-sm">
                        No team members found matching "{searchQuery}"
                      </div>
                    )}
                  </div>
                )}
              </div>
              <input type="hidden" {...register("recipientCompanyId")} />
              {errors.recipientCompanyId && (
                <p className="text-red-500 text-xs mt-1">{errors.recipientCompanyId.message}</p>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="recipientName" className="block text-sm font-medium">
                    Recipient Name
                  </label>
                  <Input
                    id="recipientName"
                    placeholder="Enter recipient's name"
                    {...register("recipientName")}
                  />
                  {errors.recipientName && (
                    <p className="text-red-500 text-xs mt-1">{errors.recipientName.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <label htmlFor="recipientEmail" className="block text-sm font-medium">
                    Recipient Email
                  </label>
                  <Input
                    id="recipientEmail"
                    type="email"
                    placeholder="Enter recipient's email"
                    {...register("recipientEmail")}
                  />
                  {errors.recipientEmail && (
                    <p className="text-red-500 text-xs mt-1">{errors.recipientEmail.message}</p>
                  )}
                </div>
              </div>
            </div>
          )}
          
          {/* Message */}
          <div className="space-y-2">
            <label htmlFor="message" className="block text-sm font-medium">
              Your Message
            </label>
            <Textarea
              id="message"
              placeholder="Write your appreciation message here..."
              rows={5}
              {...register("message")}
            />
            {errors.message && (
              <p className="text-red-500 text-xs mt-1">{errors.message.message}</p>
            )}
          </div>

          {/* Media Upload Options */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium">
                Add Personal Touch (Optional)
              </label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowMediaUpload(!showMediaUpload)}
              >
                {showMediaUpload ? 'Hide Options' : 'Add Video/Audio'}
              </Button>
            </div>

            {showMediaUpload && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium">
                      <VideoIcon className="inline h-4 w-4 mr-1" />
                      Video Message
                    </label>
                    <Input
                      placeholder="Upload or paste video URL"
                      {...register("videoMessageUrl")}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium">
                      <MicIcon className="inline h-4 w-4 mr-1" />
                      Audio Message
                    </label>
                    <Input
                      placeholder="Upload or paste audio URL"
                      {...register("audioMessageUrl")}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Gift Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium">
                Select Gifts
              </label>
              <span className="text-sm text-gray-500">
                Total: ${getTotalValue().toFixed(2)}
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {gifts.map((gift) => {
                const selectedGift = selectedGifts.find(g => g.giftId === gift.id);
                const quantity = selectedGift?.quantity || 0;

                return (
                  <div
                    key={gift.id}
                    className={`p-4 border rounded-lg transition-all ${
                      quantity > 0 ? 'border-black bg-gray-50' : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium text-sm">{gift.name}</h4>
                        <p className="text-xs text-gray-500 mt-1">{gift.description}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className="font-medium">${gift.price}</span>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            gift.category === 'branded_bundles' ? 'bg-blue-100 text-blue-800' :
                            gift.category === 'edibles' ? 'bg-orange-100 text-orange-800' :
                            gift.category === 'wellness' ? 'bg-green-100 text-green-800' :
                            gift.category === 'impact_gifts' ? 'bg-purple-100 text-purple-800' :
                            gift.category === 'experiences' ? 'bg-pink-100 text-pink-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {gift.category.replace('_', ' ')}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleGiftQuantityChange(gift.id, quantity - 1)}
                            disabled={quantity === 0}
                          >
                            <MinusIcon className="h-3 w-3" />
                          </Button>
                          <span className="w-8 text-center text-sm font-medium">{quantity}</span>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleGiftAdd(gift.id)}
                          >
                            <PlusIcon className="h-3 w-3" />
                          </Button>
                        </div>
                        {quantity > 0 && (
                          <span className="text-sm font-medium">
                            ${(gift.price * quantity).toFixed(2)}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {selectedGifts.length === 0 && (
              <p className="text-red-500 text-xs">Please select at least one gift</p>
            )}
          </div>

          {/* Scheduling Option */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium">
                Delivery Options
              </label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowScheduler(!showScheduler)}
              >
                <CalendarIcon className="h-4 w-4 mr-1" />
                {showScheduler ? 'Send Now' : 'Schedule'}
              </Button>
            </div>

            {showScheduler && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="space-y-2">
                  <label htmlFor="scheduledDate" className="block text-sm font-medium">
                    Schedule for Later
                  </label>
                  <Input
                    id="scheduledDate"
                    type="datetime-local"
                    {...register("scheduledDate", { valueAsDate: true })}
                  />
                </div>
              </div>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between items-center border-t pt-6">
          <div className="flex gap-2">
            <Button type="button" variant="outline" size="sm">
              <SaveIcon className="h-4 w-4 mr-1" />
              Save as Template
            </Button>
            <Button type="button" variant="outline" size="sm">
              Save as Draft
            </Button>
          </div>

          {/* Order Summary */}
          {selectedGifts.length > 0 && (
            <div className="text-sm text-gray-600">
              {selectedGifts.length} gift{selectedGifts.length > 1 ? 's' : ''} •
              Total: ${getTotalValue().toFixed(2)}
            </div>
          )}

          <Button type="submit" disabled={isSubmitting || selectedGifts.length === 0}>
            {isSubmitting ? 'Sending...' : showScheduler ? 'Schedule Flawagram' : 'Send Flawagram'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}