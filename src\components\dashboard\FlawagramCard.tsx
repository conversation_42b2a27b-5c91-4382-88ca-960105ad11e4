import React from 'react';
import { formatDate, truncateText } from '../../lib/utils';
import { Flawagram } from '../../types';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '../ui/Card';
import { But<PERSON> } from '../ui/Button';
import { EyeIcon, ReplyIcon, StarIcon } from 'lucide-react';

interface FlawagramCardProps {
  flawagram: Flawagram;
  type: 'received' | 'sent';
  onView?: (id: string) => void;
  onRespond?: (id: string) => void;
}

export function FlawagramCard({ flawagram, type, onView, onRespond }: FlawagramCardProps) {
  const isReceived = type === 'received';
  
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            {isReceived ? (
              <div className="w-10 h-10 rounded-full bg-gray-200 mr-3 flex items-center justify-center overflow-hidden">
                {flawagram.senderCompany.logoUrl ? (
                  <img 
                    src={flawagram.senderCompany.logoUrl} 
                    alt={flawagram.senderCompany.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-sm font-medium">
                    {flawagram.senderCompany.name.charAt(0)}
                  </span>
                )}
              </div>
            ) : (
              <div className="w-10 h-10 rounded-full bg-gray-200 mr-3 flex items-center justify-center overflow-hidden">
                {flawagram.recipientCompany.logoUrl ? (
                  <img 
                    src={flawagram.recipientCompany.logoUrl} 
                    alt={flawagram.recipientCompany.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-sm font-medium">
                    {flawagram.recipientCompany.name.charAt(0)}
                  </span>
                )}
              </div>
            )}
            <div>
              <CardTitle className="text-lg">
                {isReceived ? flawagram.senderCompany.name : flawagram.recipientCompany.name}
              </CardTitle>
              <p className="text-xs text-gray-500">
                {formatDate(flawagram.createdAt)}
              </p>
            </div>
          </div>
          {flawagram.gift && (
            <div className="flex items-center">
              <GiftIcon className="h-4 w-4 mr-1 text-gray-500" />
              <span className="text-xs text-gray-500">Gift Included</span>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <p className="text-gray-700">{truncateText(flawagram.message, 120)}</p>
      </CardContent>
      
      <CardFooter className="border-t bg-gray-50 px-4 py-3">
        <div className="flex justify-between w-full">
          <div className="flex items-center">
            <span className="text-xs text-gray-500">
              {flawagram.status === 'viewed' ? 'Viewed' : 
               flawagram.status === 'delivered' ? 'Delivered' : 
               flawagram.status === 'sent' ? 'Sent' : 'Draft'}
            </span>
          </div>
          <div className="flex gap-2">
            {onView && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => onView(flawagram.id)}
                className="text-xs"
              >
                <EyeIcon className="h-3 w-3 mr-1" />
                View
              </Button>
            )}
            {isReceived && onRespond && flawagram.status !== 'responded' && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => onRespond(flawagram.id)}
                className="text-xs"
              >
                <ReplyIcon className="h-3 w-3 mr-1" />
                Respond
              </Button>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}

function GiftIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <StarIcon {...props} />
  );
}