export interface User {
  id: string;
  name: string;
  email: string;
  avatarUrl?: string;
  role: 'admin' | 'member' | 'platform_admin';
  companyId: string;
}

export interface Company {
  id: string;
  name: string;
  logoUrl?: string;
  industry?: string;
  size?: string;
  createdAt: Date;
  members?: User[];
}

export interface Gift {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  price: number;
  category: 'branded_bundles' | 'edibles' | 'wellness' | 'impact_gifts' | 'experiences' | 'digital';
  subcategory?: string;
  isAvailable: boolean;
  inventory?: number;
  tags?: string[];
  isCustomizable?: boolean;
}

export interface Flawagram {
  id: string;
  message: string;
  gifts: Array<{
    gift: Gift;
    quantity: number;
  }>;
  senderId: string;
  senderCompanyId: string;
  senderCompany: Company;
  recipientType: 'internal' | 'external';
  recipientId?: string;
  recipientEmail?: string;
  recipientName?: string;
  recipientCompanyId?: string;
  recipientCompany?: Company;
  status: 'draft' | 'scheduled' | 'sent' | 'delivered' | 'viewed' | 'responded' | 'pending_approval';
  createdAt: Date;
  scheduledDate?: Date;
  deliveredAt?: Date;
  viewedAt?: Date;
  videoMessageUrl?: string;
  audioMessageUrl?: string;
  totalValue: number;
  isFromTemplate?: boolean;
  templateId?: string;
}

export interface FlawagramResponse {
  id: string;
  flawagramId: string;
  message: string;
  senderId: string;
  senderCompanyId: string;
  createdAt: Date;
}

export interface FlawagramFormData {
  recipientType: 'internal' | 'external';
  recipientCompanyId?: string;
  recipientEmail?: string;
  recipientName?: string;
  message: string;
  gifts: Array<{
    giftId: string;
    quantity: number;
  }>;
  scheduledDate?: Date;
  videoMessageUrl?: string;
  audioMessageUrl?: string;
  isTemplate?: boolean;
  templateName?: string;
}

export interface FlawagramTemplate {
  id: string;
  name: string;
  description: string;
  message: string;
  gifts: Array<{
    giftId: string;
    quantity: number;
  }>;
  createdBy: string;
  companyId: string;
  createdAt: Date;
  usageCount: number;
}

export interface BudgetControl {
  id: string;
  companyId: string;
  departmentId?: string;
  monthlyLimit: number;
  currentSpent: number;
  requiresApproval: boolean;
  approvers: string[];
  resetDate: Date;
}

// Community and CSR Types
export interface CommunityStory {
  id: string;
  title: string;
  description: string;
  companyId: string;
  companyName: string;
  type: 'gifting_milestone' | 'featured_sender' | 'testimonial' | 'csr_initiative';
  imageUrl?: string;
  flawagramCount?: number;
  isPublic: boolean;
  isFeatured: boolean;
  createdAt: Date;
  tags?: string[];
}

export interface CSRInitiative {
  id: string;
  title: string;
  description: string;
  companyId: string;
  companyName: string;
  industry?: string;
  region?: string;
  theme: 'education' | 'healthcare' | 'environment' | 'community' | 'other';
  impactMetrics?: {
    recipientCount?: number;
    donationAmount?: number;
    volunteersInvolved?: number;
  };
  imageUrl?: string;
  isApproved: boolean;
  isFeatured: boolean;
  createdAt: Date;
}

export interface PlatformStats {
  totalUsers: number;
  totalCompanies: number;
  totalFlawagrams: number;
  totalGiftsDelivered: number;
  monthlyActiveUsers: number;
  topGiftCategories: { category: string; count: number }[];
  topRegions: { region: string; count: number }[];
}