import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { 
  SearchIcon, 
  PlusIcon, 
  EditIcon,
  TrashIcon,
  PackageIcon,
  AlertTriangleIcon,
  CheckCircleIcon
} from 'lucide-react';
import { Gift } from '../../types';

const mockGifts: Gift[] = [
  {
    id: 'gift1',
    name: 'Custom Company Mug Set',
    description: 'Premium ceramic mugs with custom company branding',
    imageUrl: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=200&fit=crop',
    price: 35,
    category: 'branded_bundles',
    subcategory: 'drinkware',
    isAvailable: true,
    inventory: 50,
    tags: ['customizable', 'corporate'],
    isCustomizable: true,
  },
  {
    id: 'gift2',
    name: 'Artisanal Chocolate Box',
    description: 'Premium handcrafted chocolates from local chocolatiers',
    imageUrl: 'https://images.unsplash.com/photo-1593113598332-cd288d649433?w=300&h=200&fit=crop',
    price: 40,
    category: 'edibles',
    subcategory: 'chocolate',
    isAvailable: true,
    inventory: 5, // Low inventory
    tags: ['premium', 'local'],
  },
  {
    id: 'gift3',
    name: 'Spa Gift Set',
    description: 'Luxurious bath salts, oils, and wellness essentials',
    imageUrl: 'https://images.unsplash.com/photo-1593113598332-cd288d649433?w=300&h=200&fit=crop',
    price: 55,
    category: 'wellness',
    subcategory: 'self-care',
    isAvailable: false, // Out of stock
    inventory: 0,
    tags: ['luxury', 'self-care'],
  },
];

export function GiftManagement() {
  const [gifts, setGifts] = useState<Gift[]>(mockGifts);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  const filteredGifts = gifts.filter(gift => {
    const matchesSearch = gift.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         gift.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         gift.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = categoryFilter === 'all' || gift.category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  const getInventoryStatus = (inventory?: number) => {
    if (!inventory || inventory === 0) {
      return { status: 'out-of-stock', color: 'text-red-600', icon: <AlertTriangleIcon className="h-4 w-4" /> };
    } else if (inventory <= 10) {
      return { status: 'low-stock', color: 'text-yellow-600', icon: <AlertTriangleIcon className="h-4 w-4" /> };
    } else {
      return { status: 'in-stock', color: 'text-green-600', icon: <CheckCircleIcon className="h-4 w-4" /> };
    }
  };

  const getCategoryBadge = (category: string) => {
    const colors = {
      branded_bundles: 'bg-blue-100 text-blue-800',
      edibles: 'bg-orange-100 text-orange-800',
      wellness: 'bg-green-100 text-green-800',
      impact_gifts: 'bg-purple-100 text-purple-800',
      experiences: 'bg-pink-100 text-pink-800',
      digital: 'bg-gray-100 text-gray-800',
    };
    
    return `px-2 py-1 text-xs font-medium rounded-full ${colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800'}`;
  };

  const toggleAvailability = (giftId: string) => {
    setGifts(gifts.map(gift => 
      gift.id === giftId ? { ...gift, isAvailable: !gift.isAvailable } : gift
    ));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Gift Management</h1>
          <p className="text-gray-500">Manage gift inventory and catalog</p>
        </div>
        <Button>
          <PlusIcon className="h-4 w-4 mr-2" />
          Add New Gift
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search gifts, descriptions, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="h-10 rounded-md border border-gray-200 bg-white px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2"
              >
                <option value="all">All Categories</option>
                <option value="branded_bundles">Branded Bundles</option>
                <option value="edibles">Edibles</option>
                <option value="wellness">Wellness</option>
                <option value="impact_gifts">Impact Gifts</option>
                <option value="experiences">Experiences</option>
                <option value="digital">Digital</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium text-red-600">
            Inventory Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {gifts.filter(gift => !gift.inventory || gift.inventory <= 10).map(gift => (
              <div key={gift.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertTriangleIcon className="h-4 w-4 text-red-500" />
                  <span className="font-medium">{gift.name}</span>
                  <span className="text-sm text-gray-500">
                    {gift.inventory === 0 ? 'Out of stock' : `Only ${gift.inventory} remaining`}
                  </span>
                </div>
                <Button size="sm" variant="outline">
                  Restock
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Gifts Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium">
            Gift Catalog ({filteredGifts.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Gift</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Category</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Price</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Inventory</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-500">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredGifts.map((gift) => {
                  const inventoryStatus = getInventoryStatus(gift.inventory);
                  return (
                    <tr key={gift.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                            <PackageIcon className="h-6 w-6 text-gray-500" />
                          </div>
                          <div>
                            <p className="font-medium">{gift.name}</p>
                            <p className="text-sm text-gray-500 max-w-xs truncate">{gift.description}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className={getCategoryBadge(gift.category)}>
                          {gift.category.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <span className="font-medium">${gift.price}</span>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`flex items-center space-x-2 ${inventoryStatus.color}`}>
                          {inventoryStatus.icon}
                          <span className="text-sm font-medium">{gift.inventory || 0}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <button
                          onClick={() => toggleAvailability(gift.id)}
                          className={`px-2 py-1 text-xs font-medium rounded-full ${
                            gift.isAvailable 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {gift.isAvailable ? 'Available' : 'Unavailable'}
                        </button>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <EditIcon className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-600">
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
