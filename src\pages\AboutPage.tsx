import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from '../components/ui/Button';
import {
  FlowerIcon,
  HeartIcon,
  UsersIcon,
  BuildingIcon,
  ArrowRightIcon
} from 'lucide-react';

export function AboutPage() {
  return (
    <div>
      {/* Hero Section */}
      <section className="py-20 md:py-32 bg-white">
        <div className="container mx-auto px-4 text-center">
          <motion.h1
            className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            About Flawagram
          </motion.h1>

          <motion.p
            className="text-xl text-gray-500 max-w-3xl mx-auto mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            We believe that meaningful recognition builds stronger business relationships.
            Flawagram makes it beautiful and simple to show appreciation.
          </motion.p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
              <p className="text-gray-500 mb-6">
                To transform business recognition by making it more personal, meaningful,
                and impactful. We believe that when businesses take the time to truly
                appreciate their clients, partners, and employees, it creates lasting
                relationships that drive success.
              </p>
              <p className="text-gray-500">
                Flawagram provides the platform to send beautiful, thoughtful recognition
                that goes beyond a simple thank you note. It's about building connections
                that matter.
              </p>
            </motion.div>

            <motion.div
              className="bg-white p-8 rounded-lg shadow-sm border border-gray-100"
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <FlowerIcon className="h-12 w-12 mb-6" />
              <h3 className="text-xl font-semibold mb-4">Why Flawagram?</h3>
              <ul className="space-y-3 text-gray-500">
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-2 h-2 bg-black rounded-full mt-2 mr-3"></span>
                  Beautiful, professional presentation
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-2 h-2 bg-black rounded-full mt-2 mr-3"></span>
                  Easy to create and send
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-2 h-2 bg-black rounded-full mt-2 mr-3"></span>
                  Builds lasting business relationships
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-2 h-2 bg-black rounded-full mt-2 mr-3"></span>
                  Trackable engagement and impact
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Our Values</h2>
            <p className="text-gray-500 max-w-2xl mx-auto">
              The principles that guide how we build meaningful business recognition
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              className="bg-gray-50 p-6 rounded-lg text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <HeartIcon className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Authenticity</h3>
              <p className="text-gray-500">
                Recognition should be genuine and meaningful, not just another corporate gesture.
              </p>
            </motion.div>

            <motion.div
              className="bg-gray-50 p-6 rounded-lg text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <UsersIcon className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Connection</h3>
              <p className="text-gray-500">
                Every Flawagram is an opportunity to strengthen business relationships.
              </p>
            </motion.div>

            <motion.div
              className="bg-gray-50 p-6 rounded-lg text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <BuildingIcon className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Excellence</h3>
              <p className="text-gray-500">
                We're committed to creating the most beautiful and effective recognition platform.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl font-bold mb-6">Our Story</h2>
              <p className="text-gray-500 mb-6">
                Flawagram was born from a simple observation: businesses that take the time
                to meaningfully recognize their clients, partners, and employees build stronger,
                more successful relationships.
              </p>
              <p className="text-gray-500 mb-8">
                We created Flawagram to make this kind of recognition beautiful, simple,
                and impactful. Because when appreciation is done right, it transforms
                business relationships.
              </p>

              <Link to="/register">
                <Button size="lg">
                  Start Building Better Relationships
                  <ArrowRightIcon className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}
